//
//  WidgetAutoConfigurationTests.swift
//  Appio AppTests
//
//  Created by gondo on 16/09/2025.
//

@testable import Appio_App
import Testing

struct WidgetAutoConfigurationTests {
    
    @Test("Auto-configuration with single service and single widget")
    func testAutoConfigurationSingleServiceSingleWidget() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]
        
        // Create a single service with a single widget
        let service = ServiceEntity(
            id: "test_service_1",
            title: "Test Service",
            logoURL: "https://example.com/logo.png",
            customerUserId: "user123"
        )
        
        let widget = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget",
            config: """
            {
                "variants": [
                    {
                        "properties": {
                            "version": "1.0",
                            "supportedFamilies": ["systemMedium"]
                        },
                        "elements": [
                            {
                                "type": "text",
                                "properties": {
                                    "text": "Auto-configured widget"
                                }
                            }
                        ]
                    }
                ]
            }
            """
        )
        
        // Add service and widget to storage
        StorageManager.services = [service]
        StorageManager.widgets = ["test_service_1": [widget]]
        
        // Create empty configuration (simulating widget added to homescreen without configuration)
        let configuration = ConfigurationAppIntent()
        
        // Test auto-configuration
        let templateConfig = await Provider.loadTemplateConfig(for: configuration)
        
        // Verify that template config was loaded successfully
        #expect(templateConfig != nil, "Template config should be auto-loaded for single service and widget")
        #expect(templateConfig?.variants?.count == 1, "Should have one variant")
        #expect(templateConfig?.variants?.first?.elements?.count == 1, "Should have one element")
    }
    
    @Test("No auto-configuration with multiple services")
    func testNoAutoConfigurationMultipleServices() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]
        
        // Create multiple services
        let service1 = ServiceEntity(
            id: "test_service_1",
            title: "Test Service 1",
            logoURL: "https://example.com/logo1.png",
            customerUserId: "user123"
        )
        
        let service2 = ServiceEntity(
            id: "test_service_2",
            title: "Test Service 2",
            logoURL: "https://example.com/logo2.png",
            customerUserId: "user123"
        )
        
        let widget1 = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget 1",
            config: "{\"variants\": []}"
        )
        
        let widget2 = WidgetEntity(
            id: "test_widget_2",
            serviceId: "test_service_2",
            name: "Test Widget 2",
            config: "{\"variants\": []}"
        )
        
        // Add services and widgets to storage
        StorageManager.services = [service1, service2]
        StorageManager.widgets = [
            "test_service_1": [widget1],
            "test_service_2": [widget2]
        ]
        
        // Create empty configuration
        let configuration = ConfigurationAppIntent()
        
        // Test that auto-configuration doesn't happen with multiple services
        let templateConfig = await Provider.loadTemplateConfig(for: configuration)
        
        // Verify that template config is nil (no auto-configuration)
        #expect(templateConfig == nil, "Template config should be nil when multiple services exist")
    }
    
    @Test("No auto-configuration with single service but multiple widgets")
    func testNoAutoConfigurationSingleServiceMultipleWidgets() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]
        
        // Create single service with multiple widgets
        let service = ServiceEntity(
            id: "test_service_1",
            title: "Test Service",
            logoURL: "https://example.com/logo.png",
            customerUserId: "user123"
        )
        
        let widget1 = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget 1",
            config: "{\"variants\": []}"
        )
        
        let widget2 = WidgetEntity(
            id: "test_widget_2",
            serviceId: "test_service_1",
            name: "Test Widget 2",
            config: "{\"variants\": []}"
        )
        
        // Add service and widgets to storage
        StorageManager.services = [service]
        StorageManager.widgets = ["test_service_1": [widget1, widget2]]
        
        // Create empty configuration
        let configuration = ConfigurationAppIntent()
        
        // Test that auto-configuration doesn't happen with multiple widgets
        let templateConfig = await Provider.loadTemplateConfig(for: configuration)
        
        // Verify that template config is nil (no auto-configuration)
        #expect(templateConfig == nil, "Template config should be nil when multiple widgets exist for a service")
    }
    
    @Test("No auto-configuration with no services")
    func testNoAutoConfigurationNoServices() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]
        
        // Create empty configuration
        let configuration = ConfigurationAppIntent()
        
        // Test that auto-configuration doesn't happen with no services
        let templateConfig = await Provider.loadTemplateConfig(for: configuration)
        
        // Verify that template config is nil
        #expect(templateConfig == nil, "Template config should be nil when no services exist")
    }
}
