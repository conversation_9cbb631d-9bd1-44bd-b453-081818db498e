//
//  WidgetAutoConfigurationTests.swift
//  Appio AppTests
//
//  Created by gondo on 16/09/2025.
//

@testable import Appio
import Testing

struct WidgetAutoConfigurationTests {

    @Test("Storage setup for single service and single widget")
    func testStorageSetupSingleServiceSingleWidget() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]

        // Create a single service with a single widget using proper initializers
        let service = ServiceEntity(
            id: "test_service_1",
            customerUserId: "user123",
            title: "Test Service",
            description: "Test Description",
            logoURL: "https://example.com/logo.png",
            bannerURL: "https://example.com/banner.png",
            URL: "https://example.com",
            showPreview: true
        )

        let widget = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget",
            config: """
            {
                "variants": [
                    {
                        "properties": {
                            "version": "1.0",
                            "supportedFamilies": ["systemMedium"]
                        },
                        "elements": [
                            {
                                "type": "text",
                                "properties": {
                                    "text": "Auto-configured widget"
                                }
                            }
                        ]
                    }
                ]
            }
            """
        )

        // Add service and widget to storage
        StorageManager.services = [service]
        StorageManager.widgets = ["test_service_1": [widget]]

        // Verify storage setup
        #expect(StorageManager.services.count == 1, "Should have one service")
        #expect(StorageManager.widgets.count == 1, "Should have widgets for one service")
        #expect(StorageManager.widgets["test_service_1"]?.count == 1, "Should have one widget for the service")

        // Test the conditions that would trigger auto-configuration
        let availableServices = StorageManager.services.compactMap { service in
            if let widgets = StorageManager.widgets[service.id], !widgets.isEmpty {
                return service
            }
            return nil
        }

        #expect(availableServices.count == 1, "Should have exactly one service with widgets")

        if let autoService = availableServices.first,
           let widgets = StorageManager.widgets[autoService.id] {
            #expect(widgets.count == 1, "Should have exactly one widget for auto-configuration")
        }
    }

    @Test("Storage setup for multiple services")
    func testStorageSetupMultipleServices() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]

        // Create multiple services
        let service1 = ServiceEntity(
            id: "test_service_1",
            customerUserId: "user123",
            title: "Test Service 1",
            description: "Test Description 1",
            logoURL: "https://example.com/logo1.png",
            bannerURL: "https://example.com/banner1.png",
            URL: "https://example1.com",
            showPreview: true
        )

        let service2 = ServiceEntity(
            id: "test_service_2",
            customerUserId: "user123",
            title: "Test Service 2",
            description: "Test Description 2",
            logoURL: "https://example.com/logo2.png",
            bannerURL: "https://example.com/banner2.png",
            URL: "https://example2.com",
            showPreview: true
        )

        let widget1 = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget 1",
            config: "{\"variants\": []}"
        )

        let widget2 = WidgetEntity(
            id: "test_widget_2",
            serviceId: "test_service_2",
            name: "Test Widget 2",
            config: "{\"variants\": []}"
        )

        // Add services and widgets to storage
        StorageManager.services = [service1, service2]
        StorageManager.widgets = [
            "test_service_1": [widget1],
            "test_service_2": [widget2]
        ]

        // Test the conditions that would prevent auto-configuration
        let availableServices = StorageManager.services.compactMap { service in
            if let widgets = StorageManager.widgets[service.id], !widgets.isEmpty {
                return service
            }
            return nil
        }

        #expect(availableServices.count == 2, "Should have two services with widgets")
        #expect(availableServices.count != 1, "Should not trigger auto-configuration with multiple services")
    }

    @Test("Storage setup for single service with multiple widgets")
    func testStorageSetupSingleServiceMultipleWidgets() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]

        // Create single service with multiple widgets
        let service = ServiceEntity(
            id: "test_service_1",
            customerUserId: "user123",
            title: "Test Service",
            description: "Test Description",
            logoURL: "https://example.com/logo.png",
            bannerURL: "https://example.com/banner.png",
            URL: "https://example.com",
            showPreview: true
        )

        let widget1 = WidgetEntity(
            id: "test_widget_1",
            serviceId: "test_service_1",
            name: "Test Widget 1",
            config: "{\"variants\": []}"
        )

        let widget2 = WidgetEntity(
            id: "test_widget_2",
            serviceId: "test_service_1",
            name: "Test Widget 2",
            config: "{\"variants\": []}"
        )

        // Add service and widgets to storage
        StorageManager.services = [service]
        StorageManager.widgets = ["test_service_1": [widget1, widget2]]

        // Test the conditions that would prevent auto-configuration
        let availableServices = StorageManager.services.compactMap { service in
            if let widgets = StorageManager.widgets[service.id], !widgets.isEmpty {
                return service
            }
            return nil
        }

        #expect(availableServices.count == 1, "Should have one service with widgets")

        if let autoService = availableServices.first,
           let widgets = StorageManager.widgets[autoService.id] {
            #expect(widgets.count == 2, "Should have two widgets, preventing auto-configuration")
            #expect(widgets.count != 1, "Should not trigger auto-configuration with multiple widgets")
        }
    }

    @Test("Storage setup for no services")
    func testStorageSetupNoServices() async throws {
        // Setup: Clear existing data
        StorageManager.services = []
        StorageManager.widgets = [:]

        // Test the conditions that would prevent auto-configuration
        let availableServices = StorageManager.services.compactMap { service in
            if let widgets = StorageManager.widgets[service.id], !widgets.isEmpty {
                return service
            }
            return nil
        }

        #expect(availableServices.count == 0, "Should have no services with widgets")
        #expect(availableServices.count != 1, "Should not trigger auto-configuration with no services")
    }
}
