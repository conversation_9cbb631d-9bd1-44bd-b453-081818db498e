//
//  ConfigurationAppIntent.swift
//  AppioWidget
//
//  Created by gondo on 04/04/2025.
//
//  https://developer.apple.com/documentation/widgetkit/making-a-configurable-widget
//  https://developer.apple.com/videos/play/wwdc2023/10103/
//  https://www.youtube.com/watch?v=Dpa1Hcukxbc
//  https://www.youtube.com/watch?v=8To (split becaose uf note)  Do6ZX1nI
//
//  N2H: interactive widget: https://youtu.be/8To + Do6ZX1nI?feature=shared&t=7731
//

/*
 NOTES:

 @Parameter(title: "Widget Type")
 @DynamicOptionsProvider(WidgetOptionsProvider.self) - not needed since I am using WidgetQuery.IntentParameterDependency
 var widget: WidgetType?
 ...
 struct WidgetOptionsProvider: DynamicOptionsProvider {
     typealias Options = [WidgetType]

     func results(for intent: ConfigurationAppIntent) async throws -> Options {
         guard let service = intent.service else { return [] }
         let query = WidgetQuery()
         let widgets = try await query.suggestedEntities()
         return widgets.filter { $0.serviceId == service.id }
     }
 }
 ----
 func resolveWidget() async -> some AppEntityResolutionResult - this is older version. not needed since I am using WidgetQuery
 */

import AppIntents
import WidgetKit

struct ConfigurationAppIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Select Widget" // used as placeholder in physical device, instead of "Choose"
    static var description = IntentDescription("Select Widget")

    @Parameter(title: "Service")
    var serviceType: ServiceType?

    @Parameter(title: "Select Widget")
    var widgetType: WidgetType?

    // N2H: if there is only one service, display only widgetType. service count detection doesn't work
    static var parameterSummary: some ParameterSummary {
        /// When(widgetFamily: .equalTo, .systemLarge) {
        When(\.$serviceType, .hasAnyValue) {
            Summary("Select Widget") {
                \.$serviceType
                \.$widgetType
            }
        } otherwise: {
            Summary("Select Service") {
                \.$serviceType
            }
        }
    }

    // called from Siri, Shortcut, manual refresh
    func perform() async throws -> some IntentResult {
        Log.shared.warning("ConfigurationAppIntent.Perform")

        // Triggers widget (this one) refresh
        WidgetCenter.shared.reloadTimelines(ofKind: WidgetShared.intentKind)

        return .result()
    }
}

struct ServiceType: AppEntity {
    var id: String
    var title: String

    static var typeDisplayRepresentation: TypeDisplayRepresentation = "Service"
    static var defaultQuery = ServiceQuery()

    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(title)")
    }
}

struct ServiceQuery: EntityQuery {
    func entities(for identifiers: [ServiceType.ID]) async throws -> [ServiceType] {
        return Self.storedServices().filter { identifiers.contains($0.id) }
    }

    func suggestedEntities() async throws -> [ServiceType] {
        return Self.storedServices()
    }

    func defaultResult() async -> ServiceType? {
        Log.shared.warning("ServiceQuery.defaultResult called")

        if Self.storedServices().count == 1 {
            let service = Self.storedServices().first
            Log.shared.warning("Auto-selecting single service: \(service?.title ?? "unknown")")
            return service
        }
        return nil
    }

    // Get only services that has widgets
    static func storedServices() -> [ServiceType] {
        return StorageManager.services.compactMap { service in
            if let widgets = StorageManager.widgets[service.id], !widgets.isEmpty {
                return ServiceType(id: service.id, title: service.title)
            }
            return nil
        }
    }
}

struct WidgetType: AppEntity {
    var serviceId: String
    var id: String
    var name: String
    var templateConfig: String

    static var typeDisplayRepresentation: TypeDisplayRepresentation = "Widget"
    static var defaultQuery = WidgetQuery()

    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: LocalizedStringResource(stringLiteral: name))
    }
}

struct WidgetQuery: EntityQuery {
    @IntentParameterDependency<ConfigurationAppIntent>(
        \.$serviceType
    )
    var intent

    func entities(for identifiers: [WidgetType.ID]) async throws -> [WidgetType] {
        return Self.storedWidgets().filter {
            identifiers.contains($0.id)
        }
    }

    func suggestedEntities() async throws -> [WidgetType] {
        guard let intent else { return [] }
        return Self.storedWidgets().filter { $0.serviceId == intent.serviceType.id }
    }

    // NOTE: not called automatically during init, no idea why
    func defaultResult() async throws -> WidgetType? {
        Log.shared.warning("WidgetQuery.defaultResult called")

        let widgets = try await suggestedEntities()

        // If there's exactly one widget for the selected service, auto-select it
        if widgets.count == 1 {
            Log.shared.warning("Auto-selecting single widget: \(widgets.first?.name ?? "unknown")")
            return widgets.first
        }

        return nil
    }

    static func storedWidgets() -> [WidgetType] {
        return StorageManager.widgets.flatMap { serviceId, widgetEntities in
            widgetEntities.map { widgetEntity in
                WidgetType(serviceId: serviceId, id: widgetEntity.id, name: widgetEntity.name, templateConfig: widgetEntity.config)
            }
        }
    }
}
