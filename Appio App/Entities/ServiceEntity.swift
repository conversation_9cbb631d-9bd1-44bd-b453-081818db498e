//
//  ServiceEntity.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation

struct ServiceEntity: Codable, Equatable, Hashable, Identifiable {
    let id: String
    let customerUserId: String
    let title: String
    let description: String? // TODO: shouldn't be nil, just empty
    let logoURL: String
    let bannerURL: String? // TODO: shouldn't be nil, just empty
    let URL: String? // TODO: shouldn't be nil, just empty

    private var lastUpdate: Date = .now // N2H: implement, so far only placeholder
    private var lastSync: Date = .now // N2H: implement, so far only placeholder
    var notifiationsEnabled: Bool = true // N2H: implement, so far only placeholder

    private(set) var showPreview: Bool

    mutating func markPreviewViewed() {
        showPreview = false
    }

    /** // N2H: notificationsEnabled per service. once ServiceEntity setting page is created
        private var lastUpdate: Date = .now
        private var lastSync: Date = .now

        var notifiationsEnabled: Bool = false {
            willSet {
                if newValue != notifiationsEnabled {
                    lastUpdate = .now // requires storing after update
                }
            }
        }

        func isOutOfSync() -> Bool {
            return lastUpdate > lastSync
        }

        mutating func markSynced() {
            lastSync = .now // requires storing after update
        }
     */

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id, customerUserId, title, description, logoURL, bannerURL, URL, showPreview, lastUpdate, lastSync, notifiationsEnabled
    }
}

extension ServiceEntity {
    init(from response: ServiceResponse, customerUserId: String?) {
        id = response.id
        title = response.title
        description = response.description
        logoURL = response.logoURL
        bannerURL = response.bannerURL
        URL = response.URL
        showPreview = true // start with visible preview

        self.customerUserId = customerUserId ?? ""
    }

    init(from response: ServiceResponse, customerUserId: String?, existing: ServiceEntity) {
        id = response.id
        title = response.title
        description = response.description
        logoURL = response.logoURL
        bannerURL = response.bannerURL
        URL = response.URL

        self.customerUserId = customerUserId ?? existing.customerUserId
        showPreview = existing.showPreview
    }

    // Direct initializer for testing
    init(id: String, customerUserId: String, title: String, description: String?, logoURL: String, bannerURL: String?, URL: String?, showPreview: Bool) {
        self.id = id
        self.customerUserId = customerUserId
        self.title = title
        self.description = description
        self.logoURL = logoURL
        self.bannerURL = bannerURL
        self.URL = URL
        self.showPreview = showPreview
    }
}

#if DEBUG
    extension ServiceEntity {
        static var mock: ServiceEntity {
            return ServiceEntity(
                id: "svc_00000000000000000000000000",
                customerUserId: "customer-user-id-123",
                title: "Sample Service",
                description: "A description for the sample service.",
                logoURL: "https://cdn.appio.so/app/demo.appio.so/logo.png",
                bannerURL: "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
                URL: "https://www.example.com",
                showPreview: true
            )
        }

        static var mockB: ServiceEntity {
            return ServiceEntity(
                id: "svc_00000000000000001111111111",
                customerUserId: "customer-user-id-456",
                title: "Another Service",
                description: "Another description",
                logoURL: "https://cdn.appio.so/app/demo.appio.so/logo.png---",
                bannerURL: "https://cdn.appio.so/app/demo.appio.so/banner.jpg---",
                URL: nil,
                showPreview: false
            )
        }
    }
#endif
