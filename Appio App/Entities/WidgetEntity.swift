//
//  WidgetEntity.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation

struct WidgetEntity: Codable, Equatable, Hashable, Identifiable {
    let id: String
    let serviceId: String
    let name: String
    let config: String

    private var updateAt: Date = .now // N2H: to implement, so far only placeholder

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id, serviceId, name, config, updateAt
    }
}

extension WidgetEntity {
    init(from response: WidgetResponse) {
        id = response.id
        serviceId = response.serviceId
        name = response.name
        config = response.config
    }

    init(from response: WidgetResponse, existing _: WidgetEntity) {
        id = response.id
        serviceId = response.serviceId
        name = response.name
        config = response.config // overwrite with new
        // extra local fileds that should stay unchanged
    }

    // Direct initializer for testing
    init(id: String, serviceId: String, name: String, config: String) {
        self.id = id
        self.serviceId = serviceId
        self.name = name
        self.config = config
    }
}

#if DEBUG
    extension WidgetEntity {
        static var mock: WidgetEntity {
            return WidgetEntity(
                id: "wgt_00000000000000000000000000",
                serviceId: "svc_00000000000000000000000000",
                name: "tmpA",
                config: tmpConfig,
            )
        }

        static var mockB: WidgetEntity {
            return WidgetEntity(
                id: "wgt_00000000000000001111111111",
                serviceId: "svc_00000000000000000000000000",
                name: "tmpB",
                config: tmpConfig,
            )
        }

        static let ringConfig = """
        {
            "variants": [
                {
                    "properties": {
                        "version": "1.0",
                        "supportedFamilies": ["systemSmall", "systemMedium", "systemLarge", "systemExtraLarge"]
                    },
                    "elements": [
                        {
                            "type": "hstack",
                            "properties": {},
                            "elements": [
                                {
                                    "type": "spacer",
                                    "properties": {}
                                },
                                {
                                    "type": "lastUpdated",
                                    "properties": {
                                        "color": "secondary",
                                        "fontSize": 12,
                                        "padding": {
                                            "right": 10
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            "type": "gauge",
                            "properties": {
                                "value": 65,
                                "currentValueLabel": "65",
                                "style": "accessoryCircularCapacity",
                                "color": "primary",
                                "tint": "primary",
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton",
                            "properties": {
                                "text": "Refresh",
                                "style": "borderedProminent",
                                "color": "#fff",
                                "padding": {
                                    "bottom": 10
                                }
                            }
                        }
                    ]
                }
            ]
        }
        """

        static let numberConfig = """
        {
            "variants": [
                {
                    "properties": {
                        "version": "1.0",
                        "supportedFamilies": ["systemSmall", "systemMedium", "systemLarge", "systemExtraLarge"]
                    },
                    "elements": [
                        {
                            "type": "hstack",
                            "properties": {},
                            "elements": [
                                {
                                    "type": "spacer",
                                    "properties": {}
                                },
                                {
                                    "type": "lastUpdated",
                                    "properties": {
                                        "color": "secondary",
                                        "fontSize": 12,
                                        "padding": {
                                            "right": 10
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            "type": "text",
                            "properties": {
                                "text": "82",
                                "fontSize": 40,
                                "fontWeight": "semibold",
                                "color": "primary",
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton",
                            "properties": {
                                "text": "Refresh",
                                "style": "borderedProminent",
                                "color": "#fff"
                            }
                        }
                    ]
                }
            ]
        }
        """

        static let tmpConfig = """
        {
            "variants": [
                {
                    "properties": {
                        "version": "1.0",
                        "supportedFamilies": ["systemLarge"]
                    },
                    "elements": [
                        {
                            "type": "text",
                            "properties": {
                                "text": "Large widget"
                            }
                        },
                        {
                            "type": "image",
                            "properties": {
                                "src": "https://picsum.photos/300?111",
                                "height": 200
                            }
                        }
                    ]
                },
                {
                    "properties": {
                        "version": "1.0",
                        "supportedFamilies": ["systemMedium"],
                        "url": "https://appio.so",
                        "background": "#0000FF"
                    },
                    "elements": [
                        {
                            "type": "hstack",
                            "properties": {
                                "spacing": 24
                            },
                            "elements": [
                                {
                                    "type": "gauge",
                                    "properties": {
                                        "value": 85,
                                        "label": "Year of my life",
                                        "currentValueLabel": "85%",
                                        "style": "accessoryCircular",
                                        "color": "#ffff00",
                                        "tint": "#ff8800"
                                    }
                                },
                                {
                                    "type": "refreshButton",
                                    "properties": {
                                        "text": "Refresh this widget",
                                        "style": "borderedProminent",
                                        "color": "#00ffff",
                                        "tint": "#dd3388"
                                    }
                                }
                            ]
                        },
                        {
                            "type": "hstack",
                            "properties": {
                                "spacing": 24
                            },
                            "elements": [
                                {
                                    "type": "rectangle",
                                    "properties": {
                                        "color": "#ff0000",
                                        "height": 10
                                    }
                                },
                                {
                                    "type": "ellipse",
                                    "properties": {
                                        "color": "#ffff00",
                                        "width": 10,
                                        "height": 10
                                    }
                                }
                            ]
                        },
                        {
                            "type": "text",
                            "properties": {
                                "text": "Hello Widget!",
                                "fontSize": 16,
                                "color": "#000000",
                                "background": "#00ff00"
                            }
                        }
                    ]
                }
            ]
        }
        """
    }
#endif
